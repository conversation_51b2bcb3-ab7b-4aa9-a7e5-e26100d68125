"use client";
import React, { useEffect, useState, useRef } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useSelector } from "react-redux";
import { selectCurrentUser } from "../../../../../store/authSlice";
import axios from "axios";
import Link from "next/link";
import Cookies from "js-cookie";
import Image from "next/image";
// dynamic import تم نقله داخل useEffect - zaki alkholy
import PaymentForm from "../../../../_Components/PaymentForm";
import "plyr/dist/plyr.css";
import Hls from "hls.js";

// تم نقل styles الحماية إلى داخل المكون - zaki alkholy
import { useForm } from "react-hook-form";
import {
  fetchStudentCourse,
  fetchCourseLessons,
  fetchCourseReviews,
  submitCourseReview,
  fetchReviewComments,
  toggleCourseLike,
  toggleCommentLike,
  addReviewReply,
  fetchExamTimeLeft,
  fetchExamStatus, // جديد
  saveExamAnswer, // جديد
  submitExam, // جديد
  startExam, // جديد - zaki alkholy
  checkTimeAndAutoSubmit, // جديد - zaki alkholy
} from "../../../../../services/student";
import { API_BASE_URL } from '../../../../../config/api';

// Plyr يتم استيراده ديناميكياً داخل useEffect - zaki alkholy

// مودال React بسيط بدون مكتبة خارجية (تصحيح إغلاق الوسوم)
function ExamAssignmentModal({ open, onClose, type, id, duration, questions: propQuestions, examStatus, onExamSubmitted }) {
  const [questions, setQuestions] = useState([]);
  const [answers, setAnswers] = useState({});
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [result, setResult] = useState(null);
  const [timer, setTimer] = useState(0); // سيتم تعيينه في useEffect - zaki alkholy
  const [fetchError, setFetchError] = useState(null); // جديد
  const [feedbackMap, setFeedbackMap] = useState({}); // جديد: feedback لكل سؤال

  // إذا تم تمرير الأسئلة من props، استخدمها مباشرة
  useEffect(() => {
    console.log('zaki alkholy - ExamAssignmentModal useEffect:', { open, type, id, duration, timer });
    if (open) {
      setResult(null);
      setFetchError(null);
      setFeedbackMap({});

      // تحقق إذا كان examStatus يشير إلى أن الامتحان اتسلم
      if (examStatus && examStatus.status === 'submitted') {
        // عرض النتيجة من examStatus - zaki alkholy
        setResult({
          message: `تم تسليم الامتحان بنجاح! النتيجة: ${examStatus.score}/${examStatus.max_score}`,
          correctCount: examStatus.correctCount || 0,  // استخدام العدد من الباك إند - zaki alkholy
          score: examStatus.score,
          max_score: examStatus.max_score,
          answers_feedback: examStatus.answers_feedback || [],
          passed: examStatus.passed,
          totalQuestions: examStatus.totalQuestions || examStatus.questions?.length || 0  // إضافة العدد الكلي - zaki alkholy
        });
        setQuestions(examStatus.questions || []);
        setAnswers(examStatus.answers || {});
        setLoading(false);
        return;
      }

      // إزالة فحص الكوكيز - كل شيء من الباك إند - zaki alkholy
      // تحميل الإجابات المحفوظة من الباك إند فقط - zaki alkholy
      if (type === "exam") {
        // استخدام دالة async منفصلة - zaki alkholy
        (async () => {
          try {
            const token = Cookies.get("authToken");
            const timeRes = await fetchExamTimeLeft(id, token);
            if (timeRes.saved_answers) {
              // تحويل الإجابات من answer_id إلى index - zaki alkholy
              const convertedAnswers = {};
              Object.keys(timeRes.saved_answers).forEach(qid => {
                const answerId = timeRes.saved_answers[qid];
                const question = propQuestions?.find(q => q.id == qid);
                if (question && question.answers) {
                  const answerIndex = question.answers.findIndex(a => a.id == answerId);
                  if (answerIndex !== -1) {
                    convertedAnswers[qid] = answerIndex;
                  }
                }
              });
              setAnswers(convertedAnswers);
              console.log('zaki alkholy - تم تحميل الإجابات من الباك إند:', convertedAnswers);
            }
          } catch (e) {
            console.log('zaki alkholy - لم يتم العثور على إجابات محفوظة في الباك إند');
          }
        })();
      }

      // استخدم الأسئلة من props إذا توفرت
      if (Array.isArray(propQuestions) && propQuestions.length > 0) {
        setQuestions(propQuestions);
        setLoading(false);
      } else {
        setQuestions([]);
        setLoading(false);
        setFetchError("لا توجد أسئلة متاحة لهذا الامتحان/الواجب");
      }
      // تعيين الـ timer فقط للامتحانات الجارية - zaki alkholy
      if (type === "exam" && duration > 0) {
        setTimer(duration);
        console.log('zaki alkholy - تم تعيين الـ timer إلى:', duration);
      } else if (type === "exam") {
        console.log('zaki alkholy - الامتحان منتهي أو مسلم، لا يوجد timer');
      } else if (type === "assignment") {
        // الواجبات ليس لها وقت محدد - zaki alkholy
        setTimer(-1); // -1 يعني وقت غير محدود
        console.log('zaki alkholy - واجب بوقت غير محدود');
      }
    }
  }, [open, type, id, duration, propQuestions]);

  // إزالة حفظ الكوكيز - كل شيء من الباك إند - zaki alkholy

  useEffect(() => {
    if (!open || result) return;

    // للامتحانات فقط: بدء العد التنازلي - zaki alkholy
    if (type === "exam" && timer > 0) {
      const interval = setInterval(() => setTimer((t) => {
        if (t <= 1) {
          console.log('4. الوقت انتهى في الفرونت إند، سيتم التسليم التلقائي - zaki alkholy');
          // 4. التسليم التلقائي عند انتهاء الوقت في الفرونت إند - zaki alkholy
          handleAutoSubmit();
          return 0;
        }
        return t - 1;
      }), 1000);
      return () => clearInterval(interval);
    }
    // للواجبات: لا يوجد timer - zaki alkholy
    if (type === "assignment") {
      console.log('الواجب ليس له وقت محدد - zaki alkholy');
    }
  }, [timer, open, type, duration, result]);

  // عند استلام نتيجة التصحيح، جهز feedbackMap
  useEffect(() => {
    if (result && result.answers_feedback) {
      // answers_feedback: [{question_id, is_correct, correct_answer_id, selected_answer_id}]
      const map = {};
      result.answers_feedback.forEach(fb => {
        map[fb.question_id] = {
          is_correct: fb.is_correct,
          correct_answer_id: fb.correct_answer_id,
          selected_answer_id: fb.selected_answer_id,
          correct_answer_text: fb.correct_answer_text,
          selected_answer_text: fb.selected_answer_text
        };
      });
      setFeedbackMap(map);
    }
  }, [result]);

  // 3. تحديث handleChange مع منع الإرسال المتكرر - zaki alkholy
  const handleChange = (qid, choiceIdx) => {
    setAnswers((prev) => {
      // 3. تحقق من تغيير الإجابة لمنع الإرسال المتكرر للباك إند - zaki alkholy
      if (prev[qid] === choiceIdx) {
        console.log('3. الإجابة لم تتغير للسؤال، لن يتم الإرسال - zaki alkholy:', qid);
        return prev;
      }

      const updated = { ...prev, [qid]: choiceIdx };

      // 3. إرسال الإجابة للباك اند مرة واحدة فقط عند التغيير - zaki alkholy
      (async () => {
        try {
          const token = Cookies.get("authToken");
          const q = questions.find(q => q.id == qid);
          let answer_id = null;
          if (q && Array.isArray(q.answers) && q.answers[choiceIdx]) {
            answer_id = q.answers[choiceIdx].id;
          }
          console.log('3. حفظ إجابة جديدة في الباك إند - zaki alkholy:', qid, 'من', prev[qid], 'إلى', choiceIdx);
          await saveExamAnswer(id, qid, answer_id, token);
          console.log('3. تم حفظ الإجابة بنجاح في الباك إند - zaki alkholy');
        } catch (e) {
          console.error('3. خطأ في حفظ الإجابة في الباك إند - zaki alkholy:', e);
        }
      })();
      return updated;
    });
  };

  const handleSubmit = async () => {
    // منع التسليم إذا لم يتم الإجابة على أي سؤال أو إذا كان الامتحان لسه بيتحمل - zaki alkholy
    if (submitting || result || fetchError || questions.length === 0) {
      console.log('zaki alkholy - منع التسليم:', { submitting, result, fetchError, questionsLength: questions.length });
      return;
    }

    // للامتحانات، تأكد من وجود إجابات قبل التسليم - zaki alkholy
    if (type === "exam") {
      const answeredQuestions = Object.keys(answers).length;
      if (answeredQuestions === 0) {
        alert("يرجى الإجابة على سؤال واحد على الأقل قبل التسليم");
        return;
      }

      // تأكيد التسليم - zaki alkholy
      if (!confirm(`هل أنت متأكد من تسليم الامتحان؟ تم الإجابة على ${answeredQuestions} من ${questions.length} سؤال.`)) {
        return;
      }
    }

    setSubmitting(true);
    console.log('6. بدء تسليم الامتحان يدوياً - zaki alkholy:', id);
    const token = Cookies.get("authToken");
    try {
      const res = await submitExam(id, token);
      setResult(res);
      console.log('6. تم تسليم الامتحان بنجاح - zaki alkholy:', res);
      // 6. استدعاء callback لتحديث حالة الامتحان في الصفحة الرئيسية - zaki alkholy
      if (onExamSubmitted) {
        onExamSubmitted(id);
      }
    } catch (err) {
      // طباعة الخطأ الحقيقي من الباك اند
      console.error('6. خطأ في تسليم الامتحان - zaki alkholy:', err);
      if (err?.response && err.response.data) {
        alert(err.response.data.detail || err.response.data.error || JSON.stringify(err.response.data));
        console.error('تفاصيل خطأ التسليم:', err.response.data);
      } else {
        alert("حدث خطأ أثناء التسليم");
        console.error('خطأ غير معروف أثناء التسليم:', err);
      }
    }
    setSubmitting(false);
  };

  const handleAutoSubmit = async () => {
    if (submitting || result) return;

    // الواجبات ليس لها تسليم تلقائي - zaki alkholy
    if (type === "assignment") {
      console.log('الواجب ليس له تسليم تلقائي - zaki alkholy');
      return;
    }

    setSubmitting(true);
    const token = Cookies.get("authToken");

    console.log('4. بدء التسليم التلقائي للامتحان في الفرونت إند - zaki alkholy:', id);

    try {
      // 4. فحص انتهاء الوقت والتسليم التلقائي من الباك إند - zaki alkholy
      const autoSubmitRes = await checkTimeAndAutoSubmit(id, token);
      console.log('4. checkTimeAndAutoSubmit response - zaki alkholy:', autoSubmitRes);

      if (autoSubmitRes.status === 'auto_submitted') {
        // 4. تم التسليم التلقائي، جلب النتيجة - zaki alkholy
        console.log('4. تم التسليم التلقائي بنجاح، جلب النتيجة - zaki alkholy');
        const statusRes = await fetchExamStatus(id, token);
        setResult({
          message: "تم تسليم الامتحان تلقائياً لانتهاء الوقت المحدد.",
          auto_submitted: true,
          score: statusRes.score,
          max_score: statusRes.max_score,
          answers_feedback: statusRes.answers_feedback || [],
          correctCount: statusRes.answers_feedback ? statusRes.answers_feedback.filter(f => f.is_correct).length : 0,
          passed: statusRes.passed,
          totalQuestions: statusRes.totalQuestions || questions.length
        });

        // 6. تحديث حالة الامتحان في الصفحة الرئيسية - zaki alkholy
        if (onExamSubmitted) {
          onExamSubmitted(id);
        }
      } else {
        // الوقت لم ينته بعد، لا تفعل شيء - zaki alkholy
        console.log('4. الوقت لم ينته بعد في الباك إند - zaki alkholy');
      }
    } catch (err) {
      console.error('4. خطأ في التسليم التلقائي - zaki alkholy:', err);

      // في حالة فشل التحقق، جرب التسليم العادي - zaki alkholy
      try {
        const res = await submitExam(id, token);
        setResult({
          ...res,
          message: "تم تسليم الامتحان تلقائياً لانتهاء الوقت المحدد.",
          auto_submitted: true
        });

        if (onExamSubmitted) {
          onExamSubmitted(id);
        }
      } catch (submitErr) {
        console.error('4. خطأ في التسليم العادي أيضاً - zaki alkholy:', submitErr);
        setResult({
          message: "انتهى وقت الامتحان. تم حفظ إجاباتك المتاحة.",
          auto_submitted: true,
          score: 0,
          max_score: questions.length * 10,
          answers_feedback: []
        });
      }
    }
    setSubmitting(false);
  };

  const handleClose = () => {
    setResult(null);
    setAnswers({});
    setFetchError(null);
    setQuestions([]);
    setFeedbackMap({});
    onClose();
  };

  const formatTime = (seconds) => {
    const m = Math.floor(seconds / 60)
      .toString()
      .padStart(2, "0");
    const s = (seconds % 60).toString().padStart(2, "0");
    return `${m}:${s}`;
  };

  if (!open) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-lg shadow-lg max-w-2xl w-full p-6 relative animate-fadeIn">
        <button
          onClick={handleClose}
          className="absolute left-4 top-4 text-gray-400 hover:text-gray-700 text-2xl"
          aria-label="إغلاق"
        >
          ×
        </button>
        <h2 className="text-xl font-bold mb-4 text-center">
          {type === "exam" ? "امتحان" : type === "assignment" ? "واجب" : "اختبار"} الكورس
        </h2>
        {loading ? (
          <div className="text-center py-8">جاري التحميل...</div>
        ) : fetchError ? (
          <div className="text-center text-red-600 py-8">{fetchError}</div>
        ) : result ? (
          <div className="text-center space-y-4 py-6">
            {result.auto_submitted && (
              <div className="bg-yellow-100 border border-yellow-400 text-yellow-800 px-4 py-3 rounded mb-4">
                ⏰ تم تسليم الامتحان تلقائياً لانتهاء الوقت المحدد
              </div>
            )}
            <div className="space-y-3">
              <div className="text-lg">
                <b>عدد الإجابات الصحيحة:</b>
                <span className="text-green-600 font-bold"> {result.correctCount}</span>
                <span className="text-gray-600"> من {result.totalQuestions || questions.length}</span>
              </div>
              <div className="text-lg">
                <b>الدرجة النهائية:</b>
                <span className="font-bold"> {result.score} / {result.max_score}</span>
              </div>
              <div className="text-lg">
                <b>النتيجة:</b>
                <span className={result.passed ? "text-green-600 font-bold" : "text-red-600 font-bold"}>
                  {result.passed ? " نجح" : " لم ينجح"}
                </span>
              </div>
            </div>
            {/* 7. عرض تصحيح الأسئلة مع إجابات الطالب مُعلمة - zaki alkholy */}
            <div className="mt-6 text-right">
              <h3 className="text-lg font-bold mb-4 text-gray-800">تفاصيل الإجابات:</h3>
              {questions.map((q, idx) => {
                const feedback = feedbackMap[q.id];
                const isCorrect = feedback?.is_correct || false;
                const choices = Array.isArray(q.choices)
                  ? q.choices
                  : Array.isArray(q.answers)
                  ? q.answers.map(a => a.text)
                  : [];
                const selectedIdx = answers[q.id];

                // استخراج index الإجابة الصحيحة من feedback أو من الأسئلة
                let correctIdx = null;
                if (feedback?.correct_answer_id && Array.isArray(q.answers)) {
                  correctIdx = q.answers.findIndex(a => a.id === feedback.correct_answer_id);
                } else if (Array.isArray(q.answers)) {
                  correctIdx = q.answers.findIndex(a => a.is_correct);
                } else if (Array.isArray(q.choices) && q.choices.findIndex) {
                  // لو choices فيها is_correct
                  correctIdx = q.choices.findIndex(c => c.is_correct);
                }
                return (
                  <div
                    key={q.id}
                    className={`border rounded p-4 mb-2 ${
                      isCorrect === true
                        ? 'bg-green-50 border-green-400'
                        : isCorrect === false
                        ? 'bg-red-50 border-red-400'
                        : ''
                    }`}
                  >
                    <div className="mb-2 font-semibold">
                      {idx + 1}. {q.title || q.text}
                    </div>
                    <div className="space-y-2">
                      {choices.map((choice, i) => {
                        const isSelected = selectedIdx === i;
                        // هل هذا هو الاختيار الصحيح؟
                        const isRight = correctIdx === i;
                        let choiceClass = '';
                        // 8. إجابة الطالب مُعلمة بالأخضر إذا صحيحة - zaki alkholy
                        if (isSelected && isCorrect === true) choiceClass = 'text-green-700 font-bold';
                        // 8. إجابة الطالب مُعلمة بالأحمر إذا خاطئة - zaki alkholy
                        else if (isSelected && isCorrect === false) choiceClass = 'text-red-700 font-bold';
                        // إبراز الإجابة الصحيحة إذا الطالب أخطأ
                        if (isCorrect === false && isRight) choiceClass = 'bg-green-100 text-green-800 font-bold rounded px-2 py-1';
                        return (
                          <div key={i} className={choiceClass + ' flex items-center gap-2'}>
                            <input
                              type="radio"
                              name={`q_${q.id}`}
                              value={i}
                              checked={isSelected}
                              readOnly
                              disabled
                            />{' '}
                            <span>{choice}</span>
                            {/* علامة صح بجانب الإجابة الصحيحة إذا الطالب أخطأ */}
                            {isCorrect === false && isRight && (
                              <span className="ml-2 text-green-600 text-lg font-bold">✔</span>
                            )}
                          </div>
                        );
                      })}
                    </div>
                    {isCorrect === true && (
                      <div className="mt-2 text-green-700 font-bold">إجابة صحيحة</div>
                    )}
                    {isCorrect === false && (
                      <div className="mt-2 text-red-700 font-bold">إجابة خاطئة</div>
                    )}
                  </div>
                );
              })}
            </div>
            <button onClick={handleClose} className="mt-4 bg-blue-600 text-white px-6 py-2 rounded">إغلاق</button>
          </div>
        ) : (
          <>
            {type === "exam" && timer > 0 && (
              <div className="flex justify-end mb-2">
                <span className="bg-gray-100 px-3 py-1 rounded text-sm font-bold text-red-600">
                  الوقت المتبقي: {formatTime(timer)}
                </span>
              </div>
            )}
            {type === "assignment" && (
              <div className="flex justify-end mb-2">
                <span className="bg-green-100 px-3 py-1 rounded text-sm font-bold text-green-600">
                  واجب - وقت غير محدود
                </span>
              </div>
            )}
            <form
              onSubmit={(e) => {
                e.preventDefault();
                handleSubmit();
              }}
              className="space-y-6"
            >
              {questions.map((q, idx) => {
                // دعم الأسئلة القادمة من الداتا (answers) أو من API (choices)
                const choices = Array.isArray(q.choices)
                  ? q.choices
                  : Array.isArray(q.answers)
                  ? q.answers.map(a => a.text)
                  : [];
                return (
                  <div key={q.id} className="border rounded p-4 mb-2">
                    <div className="mb-2 font-semibold">
                      {idx + 1}. {q.title || q.text}
                    </div>
                    <div className="space-y-2">
                      {choices.map((choice, i) => (
                        <label key={i} className="flex items-center gap-2 cursor-pointer">
                          <input
                            type="radio"
                            name={`q_${q.id}`}
                            value={i}
                            checked={answers[q.id] === i}
                            onChange={() => handleChange(q.id, i)}
                            disabled={!!result || (type === "exam" && timer === 0)}
                          />
                          <span>{choice}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                );
              })}
              <div className="flex gap-2 justify-center mt-4">
                <button
                  type="submit"
                  className="bg-blue-600 text-white px-6 py-2 rounded disabled:opacity-50"
                  disabled={submitting || (type === "exam" && timer === 0 && duration > 0) || result}
                >
                  {submitting ? "جاري التسليم..." : type === "assignment" ? "تسليم الواجب" : "تسليم الامتحان"}
                </button>
                <button
                  type="button"
                  className="bg-gray-200 text-gray-700 px-6 py-2 rounded"
                  onClick={handleClose}
                  disabled={submitting}
                >
                  إغلاق
                </button>
              </div>
            </form>
          </>
        )}
      </div>
    </div>
  );
}

export default function CoursePage() {
  const { id } = useParams();
  const router = useRouter();
  const [course, setCourse] = useState(null);
  const [lessons, setLessons] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const user = useSelector(selectCurrentUser);
  const [isEnrolled, setIsEnrolled] = useState(false);
  const [previewVideoUrl, setPreviewVideoUrl] = useState(null);
  const [previewLoading, setPreviewLoading] = useState(false);
  const [previewError, setPreviewError] = useState(null);
  const [selectedLesson, setSelectedLesson] = useState(null);
  const [player, setPlayer] = useState(null);
  const videoRef = useRef(null);
  const [isClient, setIsClient] = useState(false);
  const [reviews, setReviews] = useState([]);
  const [reviewLoading, setReviewLoading] = useState(false);
  const [reviewError, setReviewError] = useState(null);
  const [reviewSuccess, setReviewSuccess] = useState(null);
  const { register, handleSubmit, reset } = useForm();

  // إضافة حالة الإعجاب وعدد الإعجابات
  const [isLiked, setIsLiked] = useState(false);
  const [likesCount, setLikesCount] = useState(0);
  // إضافة حالة تحميل زر الإعجاب
  const [likeLoading, setLikeLoading] = useState(false);

  // إضافة ref لمنع إعادة تعيين isLiked إلا عند أول تحميل الكورس
  const didInitLike = useRef(false);

  // Reset states when course ID changes
  useEffect(() => {
    setCourse(null);
    setLessons([]);
    setLoading(true);
    setError(null);
    setIsEnrolled(false);
    setPreviewVideoUrl(null);
    setPreviewLoading(false);
    setPreviewError(null);
    setSelectedLesson(null);
    setPlayer(null);
    setIsLiked(false);
    setLikesCount(0);
  }, [id]);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // دالة getImageUrl تم حذفها لأنها غير مستخدمة - zaki alkholy

  const getProfileImageUrl = (path) => {
    if (!path) return null;

    if (path.startsWith("http://") || path.startsWith("https://")) {
      return path;
    }

    return `${API_BASE_URL}${path.startsWith("/") ? path : `/${path}`}`;
  };

  // جلب بيانات الكورس
  useEffect(() => {
    const controller = new AbortController();
    const loadCourseData = async () => {
      try {
        const token = Cookies.get("authToken");
        if (!token) {
          setError("يرجى تسجيل الدخول للوصول إلى هذه الصفحة");
          router.push("/login");
          return;
        }
        const courseData = await fetchStudentCourse(id, token, controller.signal);
        const lessonsData = await fetchCourseLessons(id, token, controller.signal);
        setCourse(courseData);
        setLessons(lessonsData);
        if (user && courseData.students) {
          const enrolled = courseData.students.some(s => s.id === user.id);
          setIsEnrolled(enrolled);
        } else {
          setIsEnrolled(false);
        }
        if (!didInitLike.current) {
          setIsLiked(!!courseData.is_liked);
          setLikesCount(courseData.likes_count || 0);
          didInitLike.current = true;
        }
        setLoading(false);
      } catch (err) {
        if (err.name === "AbortError") return;
        if (!controller.signal.aborted) {
          if (err.response?.status === 401) {
            Cookies.remove("authToken");
            setError("انتهت صلاحية الجلسة، يرجى تسجيل الدخول مرة أخرى");
            router.push("/login");
          } else {
            setError(err.response?.data?.message || "حدث خطأ أثناء تحميل الكورس");
          }
          setLoading(false);
        }
      }
    };
    loadCourseData();
    return () => controller.abort();
  }, [id, router, user]);

  // إعادة تعيين ref عند تغيير الكورس
  useEffect(() => {
    didInitLike.current = false;
  }, [id]);

  const handleVideo = async (lesson) => {
    setPreviewLoading(true);
    setPreviewError(null);
    setSelectedLesson(lesson);

    try {
      const token = Cookies.get("authToken");
      if (!token) {
        setPreviewError("يرجى تسجيل الدخول أولاً");
        return;
      }

      // التحقق من الصلاحيات قبل جلب الفيديو - zaki alkholy
      if (!lesson.is_preview && !isEnrolled) {
        setPreviewError("يجب عليك الاشتراك في الكورس لمشاهدة هذا الفيديو");
        return;
      }

      const headers = {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
        Accept: "application/json",
      };

      console.log('جلب رابط الفيديو الآمن - zaki alkholy:', lesson.id);
      const response = await axios.get(
        `${API_BASE_URL}/api/lessons/${lesson.id}/video_url/`,
        {
          headers,
          timeout: 15000, // زيادة timeout للأمان
          validateStatus: function (status) {
            return status >= 200 && status < 500;
          },
        }
      );

      if (response.data.video_url) {
        console.log('تم جلب رابط HLS آمن - zaki alkholy');
        setPreviewVideoUrl(response.data.video_url);
      } else {
        setPreviewError("لم يتم العثور على رابط الفيديو");
      }
    } catch (err) {
      console.error("خطأ في جلب الفيديو - zaki alkholy:", err);

      if (err.code === "ECONNABORTED") {
        setPreviewError("انتهت مهلة الاتصال بالخادم");
      } else if (err.response) {
        console.error("Error response:", err.response.data);
        console.error("Error status:", err.response.status);

        if (err.response.status === 403) {
          setPreviewError("ليس لديك صلاحية لمشاهدة هذا الفيديو. يرجى الاشتراك في الكورس أولاً.");
        } else if (err.response.status === 401) {
          setPreviewError("انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى.");
          Cookies.remove("authToken");
          router.push("/login");
        } else if (err.response.status === 404) {
          setPreviewError("الفيديو غير متوفر");
        } else {
          setPreviewError(`خطأ في الخادم: ${err.response.status}`);
        }
      } else if (err.request) {
        console.error("Error request:", err.request);
        setPreviewError("لا يمكن الاتصال بالخادم");
      } else {
        console.error("Error message:", err.message);
        setPreviewError("حدث خطأ أثناء تحميل الفيديو");
      }
    } finally {
      setPreviewLoading(false);
    }
  };

  // حماية من keyboard shortcuts - zaki alkholy
  useEffect(() => {
    const handleKeyDown = (e) => {
      // منع Ctrl+S, Ctrl+Shift+I, F12, Ctrl+U
      if (
        (e.ctrlKey && e.key === 's') ||
        (e.ctrlKey && e.shiftKey && e.key === 'I') ||
        e.key === 'F12' ||
        (e.ctrlKey && e.key === 'u')
      ) {
        e.preventDefault();
        console.log('محاولة استخدام keyboard shortcut محظورة - zaki alkholy');
      }
    };

    if (previewVideoUrl) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [previewVideoUrl]);

  // تهيئة مشغل الفيديو المحمي مع HLS - zaki alkholy
  useEffect(() => {
    if (!isClient || !previewVideoUrl || !videoRef.current) return;

    // تنظيف المشغل السابق
    if (player) {
      player.destroy();
      setPlayer(null);
    }

    const video = videoRef.current;
    video.crossOrigin = "anonymous";

    console.log('تهيئة مشغل الفيديو المحمي - zaki alkholy');

    const initSecurePlayer = async () => {
      try {
        // التحقق من دعم HLS
        if (Hls.isSupported()) {
          console.log('استخدام HLS.js للفيديو المحمي - zaki alkholy');
          const hls = new Hls({
            // إعدادات الأمان - zaki alkholy
            enableWorker: true,
            lowLatencyMode: false,
            backBufferLength: 90,
            maxBufferLength: 30,
            maxMaxBufferLength: 600,
          });

          hls.loadSource(previewVideoUrl);
          hls.attachMedia(video);

          hls.on(Hls.Events.MANIFEST_PARSED, async () => {
            console.log('تم تحليل manifest بنجاح - zaki alkholy');
            // تهيئة Plyr بعد تحميل HLS
            const PlyrModule = await import("plyr");
            const newPlayer = new PlyrModule.default(video, {
              controls: [
                "play-large",
                "play",
                "progress",
                "current-time",
                "mute",
                "volume",
                "settings",
                "fullscreen",
              ],
              download: false, // منع التحميل - zaki alkholy
              hideControls: true,
              keyboard: { focused: true, global: true },
              tooltips: { controls: true, seek: true },
              quality: {
                default: 720,
                options: [1080, 720, 480, 360],
              },
              // إعدادات إضافية للحماية - zaki alkholy
              clickToPlay: true,
              disableContextMenu: true,
            });

            setPlayer(newPlayer);
          });

          hls.on(Hls.Events.ERROR, (_, data) => {
            console.error('خطأ في HLS - zaki alkholy:', data);
            if (data.fatal) {
              switch (data.type) {
                case Hls.ErrorTypes.NETWORK_ERROR:
                  setPreviewError("خطأ في الشبكة أثناء تشغيل الفيديو");
                  break;
                case Hls.ErrorTypes.MEDIA_ERROR:
                  setPreviewError("خطأ في ملف الفيديو");
                  break;
                default:
                  setPreviewError("حدث خطأ أثناء تشغيل الفيديو");
                  break;
              }
            }
          });

        } else if (video.canPlayType("application/vnd.apple.mpegurl")) {
          // Safari native HLS support
          console.log('استخدام HLS الأصلي في Safari - zaki alkholy');
          video.src = previewVideoUrl;

          video.addEventListener("loadedmetadata", async () => {
            const PlyrModule = await import("plyr");
            const newPlayer = new PlyrModule.default(video, {
              controls: [
                "play-large",
                "play",
                "progress",
                "current-time",
                "mute",
                "volume",
                "settings",
                "fullscreen",
              ],
              download: false,
              hideControls: true,
              keyboard: { focused: true, global: true },
              tooltips: { controls: true, seek: true },
              disableContextMenu: true,
            });

            setPlayer(newPlayer);
          });
        } else {
          setPreviewError("المتصفح لا يدعم تشغيل فيديوهات HLS المحمية");
        }

        // معالجة أخطاء الفيديو
        video.addEventListener("error", () => {
          setPreviewError("حدث خطأ أثناء تشغيل الفيديو");
        });

      } catch (error) {
        console.error('خطأ في تهيئة المشغل - zaki alkholy:', error);
        setPreviewError("فشل في تهيئة مشغل الفيديو");
      }
    };

    initSecurePlayer();

    return () => {
      if (player) {
        player.destroy();
      }
    };
  }, [previewVideoUrl, isClient]);

  // جلب التعليقات عند تحميل الصفحة أو تغيير الكورس
  useEffect(() => {
    if (!id) return;
    setReviewLoading(true);
    fetchCourseReviews(id)
      .then(setReviews)
      .catch(() => setReviews([]))
      .finally(() => setReviewLoading(false));
  }, [id]);

  // إرسال تعليق جديد
  const onSubmitReview = async (data) => {
    setReviewError(null);
    setReviewSuccess(null);
    try {
      const token = Cookies.get("authToken");
      await submitCourseReview(id, data, token);
      setReviewSuccess("تم إرسال التقييم بنجاح وسيظهر بعد المراجعة.");
      reset();
      const res = await fetchCourseReviews(id);
      setReviews(res);
    } catch (err) {
      setReviewError("حدث خطأ أثناء إرسال التقييم");
    }
  };

  // دالة تبديل الإعجاب
  const handleToggleLike = async () => {
    if (!user) {
      router.push("/login");
      return;
    }
    try {
      setLikeLoading(true);
      const token = Cookies.get("authToken");
      const res = await toggleCourseLike(id, token);
      setIsLiked(res.is_liked ?? res.liked ?? false);
      setLikesCount(res.likes_count ?? res.likesCount ?? 0);
    } catch (err) {
      console.error('toggle_like error:', err);
    } finally {
      setLikeLoading(false);
    }
  };

  // دالة جلب التعليقات الشجرية (تُستخدم في useEffect أو عند تحديث التعليقات)
  const fetchReviewCommentsHandler = async (reviewId) => {
    try {
      return await fetchReviewComments(reviewId);
    } catch {
      return [];
    }
  };

  // دالة إعجاب/إلغاء إعجاب للتعليق أو الرد
  const handleToggleCommentLike = async (commentId) => {
    try {
      const token = Cookies.get("authToken");
      const res = await toggleCommentLike(commentId, token);
      setReviews((prev) => prev.map((review) => ({
        ...review,
        comments: review.comments?.map((c) =>
          updateCommentLikeRecursive(c, commentId, res)
        ) || [],
      })));
    } catch (err) {
      // يمكن عرض رسالة خطأ
    }
  };

  // دالة إضافة رد
  const handleAddReply = async (parentId, replyText, reviewId) => {
    try {
      const token = Cookies.get("authToken");
      await addReviewReply(reviewId, parentId, replyText, token);
      const comments = await fetchReviewCommentsHandler(reviewId);
      setReviews((prev) => prev.map((review) =>
        review.id === reviewId ? { ...review, comments } : review
      ));
    } catch (err) {
      // يمكن عرض رسالة خطأ
    }
  };

  // مكون عرض التعليقات الشجرية
  function CommentTree({ comments, reviewId }) {
    const [replyingTo, setReplyingTo] = useState(null);
    const [replyText, setReplyText] = useState("");
    return (
      <div>
        {comments?.map((comment) => (
          <div key={comment.id} className="mt-2 p-2 bg-gray-100 rounded ml-2">
            <div className="flex justify-between items-center">
              <span className="text-gray-800">{comment.user?.username || "مستخدم"}: {comment.text}</span>
              <div className="flex items-center gap-2">
                <button
                  className={`text-sm ${comment.is_liked ? 'text-blue-600' : 'text-gray-600'}`}
                  onClick={() => handleToggleCommentLike(comment.id)}
                >
                  👍 {comment.likes_count || 0}
                </button>
                <button
                  className="text-sm text-green-600"
                  onClick={() => setReplyingTo(comment.id)}
                >رد</button>
              </div>
            </div>
            {replyingTo === comment.id && (
              <form
                className="flex gap-2 mt-2"
                onSubmit={e => {
                  e.preventDefault();
                  if (replyText.trim()) {
                    handleAddReply(comment.id, replyText, reviewId);
                    setReplyText("");
                    setReplyingTo(null);
                  }
                }}
              >
                <input
                  className="border rounded p-1 flex-1"
                  value={replyText}
                  onChange={e => setReplyText(e.target.value)}
                  placeholder="اكتب ردك..."
                />
                <button type="submit" className="bg-primary text-white px-2 rounded">إرسال</button>
                <button type="button" className="text-gray-500" onClick={() => setReplyingTo(null)}>إلغاء</button>
              </form>
            )}
            {/* عرض الردود بشكل شجري */}
            {comment.replies && comment.replies.length > 0 && (
              <CommentTree comments={comment.replies} reviewId={reviewId} />
            )}
          </div>
        ))}
      </div>
    );
  }

  // إضافة state للمودال (يجب أن تكون داخل دالة CoursePage)
  const [modalOpen, setModalOpen] = useState(false);
  const [modalType, setModalType] = useState(null); // 'exam' | 'assignment'
  const [modalId, setModalId] = useState(null);
  const [modalDuration, setModalDuration] = useState(0);
  const [modalQuestions, setModalQuestions] = useState([]); // state جديد للأسئلة الممررة للمودال

  // تعديل فتح مودال الامتحان لتنفيذ المتطلبات - zaki alkholy
  const handleOpenExamModal = async (examIdParam) => {
    console.log('1. زر حل الامتحان تم الضغط عليه - zaki alkholy');
    try {
      const token = Cookies.get("authToken");
      const usedExamId = examIdParam || examId;
      console.log('examId:', usedExamId);
      console.log('token:', token);
      if (token && usedExamId) {
        // 1. تحقق من حالة الامتحان أولاً - zaki alkholy
        const statusRes = await fetchExamStatus(usedExamId, token);
        console.log('zaki alkholy - fetchExamStatus response:', statusRes);

        if (statusRes.status === 'submitted') {
          // 7. الامتحان اتسلم خلاص، اعرض النتيجة مع إجابات الطالب - zaki alkholy
          console.log('7. الامتحان مُسلم، سيتم عرض النتيجة - zaki alkholy');
          setModalType('exam');
          setModalId(usedExamId);
          setModalDuration(0); // مافيش وقت لأن الامتحان خلص
          setModalQuestions(statusRes.questions || []);
          setModalOpen(true);
        } else {
          // الامتحان لسه مش اتسلم، ابدأ الامتحان أو استكمل - zaki alkholy
          try {
            // 1. استدعاء start endpoint لتسجيل دخول الطالب في وقت كذا - zaki alkholy
            const startRes = await startExam(usedExamId, token);
            console.log('1. تم تسجيل دخول الطالب للامتحان - zaki alkholy:', startRes);
          } catch (startErr) {
            if (startErr?.response?.status === 403) {
              // الامتحان تم تسليمه بالفعل
              console.log('zaki alkholy - الامتحان تم تسليمه بالفعل');
              const updatedStatus = await fetchExamStatus(usedExamId, token);
              setModalType('exam');
              setModalId(usedExamId);
              setModalDuration(0);
              setModalQuestions(updatedStatus.questions || []);
              setModalOpen(true);
              return;
            }
          }

          // جلب الوقت المتبقي - zaki alkholy
          const res = await fetchExamTimeLeft(usedExamId, token);
          console.log('zaki alkholy - fetchExamTimeLeft response:', res);
          console.log('zaki alkholy - res.questions:', res.questions);

          if (res.status === 'auto_submitted') {
            // 5. تم التسليم التلقائي في الباك إند، اعرض النتيجة - zaki alkholy
            alert(res.message);
            // إعادة جلب حالة الامتحان لعرض النتيجة
            const updatedStatus = await fetchExamStatus(usedExamId, token);
            setModalType('exam');
            setModalId(usedExamId);
            setModalDuration(0);
            setModalQuestions(updatedStatus.questions || []);
            setModalOpen(true);
          } else if (res.time_left === -1) {
            // واجب بوقت غير محدود - zaki alkholy
            console.log('2. فتح الواجب بوقت غير محدود - zaki alkholy');
            setModalType('assignment');
            setModalId(usedExamId);
            setModalDuration(-1); // وقت غير محدود
            setModalQuestions(res.questions || []);
            setModalOpen(true);
          } else if (res.time_left > 0) {
            console.log('2. فتح الامتحان مع الوقت المتبقي - zaki alkholy:', res.time_left);
            // 2. إظهار المودال للطالب لبدء الامتحان - zaki alkholy
            setModalType('exam');
            setModalId(usedExamId);
            setModalDuration(res.time_left); // الوقت المتبقي الصحيح - zaki alkholy
            setModalQuestions(res.questions || []);
            setModalOpen(true);
          } else {
            alert("انتهى وقت الامتحان أو لا يمكنك الدخول.");
          }
        }
      }
    } catch (e) {
      alert(e?.response?.data?.error || "لا يمكنك دخول الامتحان أو انتهى الوقت.");
    }
  };

  // إضافة حالة الامتحان examStatus في CoursePage
  const [examStatus, setExamStatus] = useState(null);

  // جلب حالة الامتحان عند تحميل الصفحة أو تغيير الكورس
  const examId = course?.exam_id;
  const assignmentId = course?.assignment_id;
  useEffect(() => {
    if (!examId) return;
    const fetchStatus = async () => {
      try {
        const token = Cookies.get("authToken");
        if (!token) return;
        const status = await fetchExamStatus(examId, token);
        setExamStatus(status);
      } catch (e) {
        console.error("تعذر جلب حالة الامتحان - zaki alkholy:", e);
      }
    };
    fetchStatus();
  }, [examId]);

  // حالة كل كويز لكل درس
  const [quizStatuses, setQuizStatuses] = useState({});

  // جلب حالة كل كويز عند تحميل الدروس
  useEffect(() => {
    if (!lessons || !user) return;
    const token = Cookies.get("authToken");
    const fetchStatuses = async () => {
      const statuses = {};
      for (const lesson of lessons) {
        if (lesson.quizzes && lesson.quizzes.length > 0) {
          for (const quiz of lesson.quizzes) {
            try {
              const res = await fetchExamStatus(quiz.id, token);
              statuses[quiz.id] = res;
            } catch (e) {
              statuses[quiz.id] = null;
            }
          }
        }
      }
      setQuizStatuses(statuses);
    };
    fetchStatuses();
  }, [lessons, user]);

  // 6. دالة لتحديث حالة الامتحان بعد التسليم - zaki alkholy
  const handleExamSubmitted = async (quizId) => {
    try {
      const token = Cookies.get("authToken");
      if (!token) return;

      console.log('6. تحديث حالة الامتحان بعد التسليم - zaki alkholy:', quizId);
      // 6. تحديث حالة الكويز المحدد لتغيير الزر من "حل الامتحان" إلى "إظهار النتيجة" - zaki alkholy
      const updatedStatus = await fetchExamStatus(quizId, token);
      setQuizStatuses(prev => ({
        ...prev,
        [quizId]: updatedStatus
      }));

      // إذا كان هذا هو الامتحان الرئيسي للكورس، حدث examStatus أيضاً
      if (quizId === examId) {
        setExamStatus(updatedStatus);
      }
      console.log('6. تم تحديث حالة الامتحان بنجاح - zaki alkholy');
    } catch (e) {
      console.error('6. خطأ في تحديث حالة الامتحان - zaki alkholy:', e);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-red-500 text-xl">{error}</div>
      </div>
    );
  }

  if (!course) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-gray-500 text-xl">لم يتم العثور على الكورس</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 pt-20">
      {/* إضافة styles الحماية في head - zaki alkholy */}
      <style jsx global>{`
        .video-protected {
          -webkit-user-select: none;
          -moz-user-select: none;
          -ms-user-select: none;
          user-select: none;
          -webkit-touch-callout: none;
          -webkit-tap-highlight-color: transparent;
          pointer-events: auto;
        }

        .video-protected video {
          pointer-events: auto;
        }

        /* منع النقر بالزر الأيمن */
        .video-protected video::-webkit-media-controls {
          display: none !important;
        }

        .video-protected video::-webkit-media-controls-enclosure {
          display: none !important;
        }

        /* منع حفظ الفيديو */
        .video-protected video::-webkit-media-controls-download-button {
          display: none !important;
        }

        .video-protected video::-webkit-media-controls-fullscreen-button {
          display: none !important;
        }
      `}</style>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          {/* صورة الكورس */}
          <div className="relative h-96 w-full bg-gray-200 flex items-center justify-center">
            {course.thumbnail ? (
              <Image
                src={`https://res.cloudinary.com/di5y7hnub/${course.thumbnail}`}
                alt={course.title}
                fill
                className="object-cover"
                priority
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center bg-gray-100">
                <span className="text-gray-500 text-xl">
                  لا توجد صورة للكورس
                </span>
              </div>
            )}
          </div>

          {/* معلومات الكورس */}
          <div className="p-6">
            <div className="flex justify-between items-start mb-6">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 mb-2">
                  {course.title}
                </h1>
                <p className="text-gray-600 text-lg">
                  {course.short_description}
                </p>
              </div>
              <div className="flex flex-col items-end gap-2">
                <div className="flex items-center space-x-4 rtl:space-x-reverse">
                  <div className="flex items-center">
                    <svg
                      className="w-5 h-5 text-yellow-400"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                    <span className="mr-1 text-gray-700">
                      {course.rating || 0}
                    </span>
                  </div>
                  <div className="flex items-center">
                    <svg
                      className="w-5 h-5 text-gray-500"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path d="M18 8a2 2 0 11-4 0 2 2 0 014 0z" />
                      <path d="M14 15a4 4 0 00-8 0v3h8v-3z" />
                      <path d="M6 8a2 2 0 11-4 0 2 2 0 014 0z" />
                      <path d="M16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3z" />
                      <path d="M4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />
                    </svg>
                    <span className="mr-1 text-gray-700">
                      {course.students_count || 0} طالب
                    </span>
                  </div>
                </div>
                {/* زر الإعجاب وعداد الإعجابات */}
                <button
                  onClick={handleToggleLike}
                  disabled={likeLoading}
                  className={`flex items-center gap-1 px-3 py-1 rounded transition-colors ${isLiked ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700'} hover:bg-blue-200 ${likeLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  <svg
                    className={`w-5 h-5 ${isLiked ? 'fill-blue-500' : 'fill-gray-400'}`}
                    viewBox="0 0 20 20"
                  >
                    <path d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 18.657l-6.828-6.829a4 4 0 010-5.656z" />
                  </svg>
                  <span>{isLiked ? 'إلغاء الإعجاب' : 'إعجاب'}</span>
                  <span className="ml-2 text-xs">{likesCount}</span>
                  {likeLoading && (
                    <span className="ml-2 animate-spin w-4 h-4 border-b-2 border-blue-500 rounded-full inline-block"></span>
                  )}
                </button>
              </div>
            </div>

            {/* صورة المدرس */}
            <div className="flex items-center mb-6 p-4 bg-gray-50 rounded-lg">
              <div className="w-16 h-16 mr-4 bg-gray-200 rounded-full flex items-center justify-center overflow-hidden">
                {course.instructor?.profile_image ? (
                  <Image
                    src={getProfileImageUrl(course.instructor.profile_image)}
                    alt={course.instructor?.username || "صورة المدرس"}
                    width={64}
                    height={64}
                    className="rounded-full object-cover"
                    sizes="64px"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center bg-gray-100">
                    <span className="text-gray-500 text-xs">صورة</span>
                  </div>
                )}
              </div>
              <div>
                <p className="text-gray-900 font-medium text-lg">
                  {course.instructor?.username}
                </p>
                <p className="text-gray-600">{course.instructor?.bio}</p>
              </div>
            </div>

            <div className="prose max-w-none mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                وصف الكورس
              </h2>
              <p className="text-gray-600 leading-relaxed">
                {course.description}
              </p>
            </div>

            {/* تفاصيل الكورس */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  المستوى
                </h3>
                <p className="text-gray-600">{course.level}</p>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  اللغة
                </h3>
                <p className="text-gray-600">{course.language}</p>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  السعر
                </h3>
                <p className="text-gray-600">
                  {course.discount_price ? (
                    <>
                      <span className="line-through text-gray-400">
                        {course.price}
                      </span>
                      <span className="text-primary mr-2">
                        {course.discount_price}
                      </span>
                    </>
                  ) : (
                    <span>{course.price}</span>
                  )}
                  {course.currency}
                </p>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  عدد الدروس
                </h3>
                <p className="text-gray-600">{lessons.length} درس</p>
              </div>
            </div>

            {/* متطلبات الكورس */}
            {course.prerequisites && (
              <div className="mb-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">
                  متطلبات الكورس
                </h2>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-gray-600">{course.prerequisites}</p>
                </div>
              </div>
            )}

            {/* مخرجات التعلم */}
            {course.learning_outcomes && (
              <div className="mb-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">
                  مخرجات التعلم
                </h2>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-gray-600">{course.learning_outcomes}</p>
                </div>
              </div>
            )}

            {/* محتوى الكورس */}
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                محتوى الكورس
              </h2>
              <div className="space-y-4">
                {Array.isArray(lessons) && lessons.length > 0 ? (
                  lessons.map((lesson, index) => (
                    <div
                      key={lesson.id}
                      className="p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <span className="text-gray-500 ml-3">{index + 1}</span>
                          <h3 className="text-lg font-medium text-gray-900">
                            {lesson.title}
                          </h3>
                          {lesson.is_preview && (
                            <span className="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded mr-2">
                              معاينة
                            </span>
                          )}
                        </div>
                        <div className="flex items-center space-x-4 rtl:space-x-reverse">
                          <span className="text-gray-500">
                            {lesson.duration} دقيقة
                          </span>
                          <span className="text-gray-500">
                            {lesson.lesson_type}
                          </span>
                          {(lesson.is_preview || isEnrolled) && (
                            <button
                              onClick={() => handleVideo(lesson)}
                              className="inline-flex items-center px-3 py-1.5 text-sm font-medium text-primary bg-primary/10 rounded-lg hover:bg-primary/20 transition-colors"
                            >
                              <svg
                                className="w-4 h-4 ml-1"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                                />
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                                />
                              </svg>
                              {lesson.is_preview ? "معاينة" : "مشاهدة"}
                            </button>
                          )}
                        </div>
                      </div>

                      {/* روابط الامتحان/الواجب/الملف لكل درس */}
                      <div className="flex flex-wrap gap-3 mt-3">
                        {/* أزرار الامتحان/الواجب - zaki alkholy */}
                        {lesson.quizzes && lesson.quizzes.length > 0 && isEnrolled && lesson.quizzes.map((quiz) => {
                          // 6. منطق إظهار زر "إظهار النتيجة" إذا الطالب سلّم الامتحان - zaki alkholy
                          const quizStatus = quizStatuses[quiz.id];
                          const isSubmitted = quizStatus?.status === 'submitted';
                          return (
                            isSubmitted ? (
                              // 7. زر "إظهار النتيجة" - يظهر بعد تسليم الامتحان - zaki alkholy
                              <button
                                key={quiz.id}
                                onClick={() => handleOpenExamModal(quiz.id)}
                                className="bg-green-600 text-white px-3 py-1 rounded"
                              >
                                إظهار النتيجة
                              </button>
                            ) : (
                              // زر "حل الامتحان" - يظهر قبل التسليم - zaki alkholy
                              <button
                                key={quiz.id}
                                onClick={() => handleOpenExamModal(quiz.id)}
                                className={
                                  quiz.quiz_type === 'exam'
                                    ? 'bg-blue-600 text-white px-3 py-1 rounded'
                                    : 'bg-green-600 text-white px-3 py-1 rounded'
                                }
                              >
                                {quiz.quiz_type === 'exam' ? 'حل الامتحان' : 'حل الواجب'}
                              </button>
                            )
                          );
                        })}
                        {/* زر تحميل ملف الدرس */}
                        {lesson.resources && isEnrolled&& (
                          <a
                            href={lesson.resources}
                            target="_blank"
                            rel="noopener noreferrer"
                            download
                            className="bg-gray-700 text-white px-3 py-1 rounded"
                          >
                            تحميل ملف الدرس
                          </a>
                        )}
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-gray-500 text-center">
                    لا توجد دروس متاحة حالياً
                  </p>
                )}
              </div>
            </div>

            {/* التقييمات والتعليقات */}
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">تقييمات الطلاب</h2>
              {reviewLoading ? (
                <div>جاري التحميل...</div>
              ) : reviews.length === 0 ? (
                <div className="text-gray-500">لا توجد تقييمات بعد</div>
              ) : (
                <div className="space-y-4">
                  {reviews.map((review) => (
                    <div key={review.id} className="bg-gray-50 p-4 rounded-lg">
                      <div className="flex items-center mb-2">
                        <span className="font-bold text-gray-800 mr-2">{review.user?.username || "مستخدم"}</span>
                        <span className="text-yellow-500 ml-2">{'★'.repeat(review.rating)}{'☆'.repeat(5 - review.rating)}</span>
                      </div>
                      <div className="text-gray-700">{review.comment}</div>
                      <div className="text-xs text-gray-400 mt-1">{new Date(review.created_at).toLocaleDateString()}</div>
                      {review.reply && (
                        <div className="mt-2 p-2 bg-blue-50 rounded text-blue-800">
                          <span><span className="font-bold">رد المدرب:</span> {review.reply}</span>
                        </div>
                      )}
                      {/* عرض التعليقات الشجرية */}
                      <CommentTree comments={review.comments} reviewId={review.id} />
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* فورم إضافة تقييم */}
            {user && isEnrolled && (
              <div className="mb-8">
                <h3 className="text-lg font-bold mb-2">أضف تقييمك للكورس</h3>
                <form onSubmit={handleSubmit(onSubmitReview)} className="space-y-2">
                  <div>
                    <label className="block mb-1">عدد النجوم:</label>
                    <select {...register("rating", { required: true })} className="border rounded p-2">
                      <option value="">اختر</option>
                      {[1,2,3,4,5].map(n => <option key={n} value={n}>{n}</option>)}
                    </select>
                  </div>
                  <div>
                    <label className="block mb-1">تعليقك:</label>
                    <textarea {...register("comment", { required: true })} className="border rounded p-2 w-full" rows={3} />
                  </div>
                  <button type="submit" className="bg-primary text-white px-4 py-2 rounded">إرسال</button>
                  {reviewError && <div className="text-red-500 mt-2">{reviewError}</div>}
                  {reviewSuccess && <div className="text-green-600 mt-2">{reviewSuccess}</div>}
                </form>
              </div>
            )}

            {/* زر التسجيل */}
            {user ? (
              isEnrolled ? (
                <div className="text-center">
                  <p className="text-green-600 mb-4">أنت مسجل في هذا الكورس</p>
                  <Link
                    href={`/student/course/${id}/learn`}
                    className="inline-block bg-primary text-white py-4 px-6 rounded-lg font-medium text-lg hover:bg-primary/90 transition-colors"
                  >
                    متابعة التعلم
                  </Link>
                </div>
              ) : (
                <>
                  {id ? (
                    <PaymentForm
                      courseId={id}
                      coursePrice={course?.discount_price || course?.price}
                    />
                  ) : (
                    <div className="text-red-500 text-center">
                      خطأ: معرّف الكورس غير متوفر
                    </div>
                  )}
                </>
              )
            ) : (
              <div className="text-center">
                <p className="text-gray-600 mb-4">
                  يجب تسجيل الدخول للتسجيل في الكورس
                </p>
                <Link
                  href="/login"
                  className="inline-block bg-primary text-white py-4 px-6 rounded-lg font-medium text-lg hover:bg-primary/90 transition-colors"
                >
                  تسجيل الدخول
                </Link>
              </div>
            )}

            {/* أزرار حل الامتحان/الواجب */}
            <div className="flex gap-4 mb-8">
              {examId && (
                examStatus?.submitted || examStatus?.time_left === 0 ? (
                  <button
                    type="button"
                    onClick={() => handleOpenExamModal(examId)}
                    className="bg-green-600 text-white px-4 py-2 rounded"
                  >
                    عرض النتيجة - {course?.exam_name}
                  </button>
                ) : (
                  <button
                    type="button"
                    onClick={() => handleOpenExamModal(examId)}
                    className="bg-blue-600 text-white px-4 py-2 rounded"
                  >
                    حل الامتحان - {course?.exam_name}
                  </button>
                )
              )}
              {assignmentId && (
                <button
                  type="button"
                  onClick={() => {
                    setModalType("assignment");
                    setModalId(assignmentId);
                    setModalDuration(0);
                    setModalOpen(true);
                  }}
                  className="bg-green-600 text-white px-4 py-2 rounded"
                >
                  حل الواجب
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* مودال الامتحان/الواجب */}
      <ExamAssignmentModal
        open={modalOpen}
        onClose={() => setModalOpen(false)}
        type={modalType}
        id={modalId}
        duration={modalType === "exam" ? modalDuration : undefined}
        questions={modalQuestions}
        examStatus={modalId === examId ? examStatus : quizStatuses[modalId]} // تمرير حالة الامتحان الصحيحة
        onExamSubmitted={handleExamSubmitted} // تمرير دالة callback
      />

      {previewVideoUrl && isClient && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg w-full max-w-4xl">
            <div className="p-4 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold text-gray-900">
                {selectedLesson?.title} -{" "}
                {selectedLesson?.is_preview ? "معاينة" : "مشاهدة"}
              </h3>
              <button
                onClick={() => {
                  setPreviewVideoUrl(null);
                  setSelectedLesson(null);
                  if (player) {
                    player.destroy();
                    setPlayer(null);
                  }
                }}
                className="text-gray-500 hover:text-gray-700"
              >
                <svg
                  className="w-6 h-6"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>
            <div className="p-4">
              {previewLoading ? (
                <div className="flex justify-center items-center h-64">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
                </div>
              ) : previewError ? (
                <div className="text-red-500 text-center p-4">
                  {previewError}
                </div>
              ) : (
                <div
                  className="plyr__video-embed relative video-protected"
                  onContextMenu={(e) => e.preventDefault()} // منع النقر بالزر الأيمن - zaki alkholy
                  onDragStart={(e) => e.preventDefault()} // منع السحب - zaki alkholy
                  style={{ userSelect: 'none' }} // منع التحديد - zaki alkholy
                >
                  <video
                    ref={videoRef}
                    className="w-full rounded-lg"
                    style={{ maxHeight: "70vh" }}
                    playsInline
                    controls={false} // إخفاء controls الافتراضية لاستخدام Plyr - zaki alkholy
                    controlsList="nodownload nofullscreen noremoteplayback" // منع التحميل - zaki alkholy
                    disablePictureInPicture // منع picture-in-picture - zaki alkholy
                    onContextMenu={(e) => e.preventDefault()}
                    crossOrigin="anonymous" // للأمان - zaki alkholy
                    onLoadStart={() => console.log('بدء تحميل الفيديو المحمي - zaki alkholy')}
                    onError={() => console.error('خطأ في تحميل الفيديو المحمي - zaki alkholy')}
                    // منع التحديد والسحب - zaki alkholy
                    draggable={false}
                    onSelectStart={(e) => e.preventDefault()}
                    onDragStart={(e) => e.preventDefault()}
                  />
                  {/* طبقة حماية شفافة - zaki alkholy */}
                  <div
                    className="absolute inset-0 pointer-events-none rounded-lg"
                    style={{
                      background: 'transparent',
                      zIndex: 1
                    }}
                  />
                  {/* رسالة تحذيرية للحماية - zaki alkholy */}
                  <div className="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded flex items-center gap-1">
                    <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                    </svg>
                    محتوى محمي
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
